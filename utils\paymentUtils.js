/**
 * 统一支付工具类
 * 支持微信小程序支付和APP微信支付
 * <AUTHOR>
 * @version 1.0.0
 */

class PaymentUtils {
	constructor() {
		this.platform = this.getCurrentPlatform();
	}

	/**
	 * 检查当前平台
	 * @returns {string} 平台类型
	 */
	getCurrentPlatform() {
		// #ifdef APP-PLUS
		return 'app-plus';
		// #endif
		// #ifdef MP-WEIXIN
		return 'mp-weixin';
		// #endif
		// #ifdef H5
		return 'h5';
		// #endif
		return 'unknown';
	}

	/**
	 * 统一支付方法
	 * @param {Object} options 支付配置选项
	 * @param {Object} options.paymentData 支付数据对象
	 * @param {Function} options.onSuccess 支付成功回调
	 * @param {Function} options.onFail 支付失败回调
	 * @param {Function} options.onCancel 支付取消回调
	 * @param {Object} options.subscribeConfig 订阅消息配置（可选）
	 * @param {string} options.successRedirectUrl 支付成功后跳转的页面（可选）
	 * @returns {Promise}
	 */
	async unifiedPay(options = {}) {
		const {
			paymentData,
			onSuccess,
			onFail,
			onCancel,
			subscribeConfig,
			successRedirectUrl = '/user/order_list?tab=0'
		} = options;

		if (!paymentData) {
			throw new Error('支付数据不能为空');
		}

		try {
			console.log('当前平台:', this.platform);
			console.log('支付数据:', paymentData);

			// 根据平台选择不同的支付方式
			if (this.platform === 'app-plus') {
				return await this.handleAppWechatPay(paymentData, {
					onSuccess,
					onFail,
					onCancel,
					subscribeConfig,
					successRedirectUrl
				});
			} else if (this.platform === 'mp-weixin') {
				return await this.handleMiniProgramPay(paymentData, {
					onSuccess,
					onFail,
					onCancel,
					subscribeConfig,
					successRedirectUrl
				});
			} else if (this.platform === 'h5') {
				return await this.handleH5WechatPay(paymentData, {
					onSuccess,
					onFail,
					onCancel,
					successRedirectUrl
				});
			} else {
				throw new Error('不支持的平台类型');
			}
		} catch (error) {
			console.error('支付过程中发生错误:', error);
			if (onFail) {
				onFail(error);
			} else {
				uni.showToast({
					title: '支付失败，请稍后重试',
					icon: 'none'
				});
			}
			throw error;
		}
	}

	/**
	 * APP微信支付处理
	 * @param {Object} paymentData 支付数据
	 * @param {Object} callbacks 回调函数集合
	 */
	async handleAppWechatPay(paymentData, callbacks = {}) {
		const { onSuccess, onFail, onCancel, subscribeConfig, successRedirectUrl } = callbacks;

		return new Promise((resolve, reject) => {
			uni.requestPayment({
				provider: "wxpay",
				orderInfo: {
					appid: paymentData.appId,
					noncestr: paymentData.nonceStr,
					package: 'Sign=WXPay',
					partnerid: paymentData.partnerId,
					prepayid: paymentData.prepayId,
					timestamp: String(paymentData.timestamp),
					sign: paymentData.sign
				},
				success: (res) => {
					console.log('APP微信支付成功', res);
					this.handlePaymentSuccess(onSuccess, subscribeConfig, successRedirectUrl);
					resolve(res);
				},
				fail: (err) => {
					console.error('APP微信支付失败:', err);
					this.handlePaymentError(err, onFail, onCancel);
					reject(err);
				}
			});
		});
	}

	/**
	 * 微信小程序支付处理
	 * @param {Object} paymentData 支付数据
	 * @param {Object} callbacks 回调函数集合
	 */
	async handleMiniProgramPay(paymentData, callbacks = {}) {
		const { onSuccess, onFail, onCancel, subscribeConfig, successRedirectUrl } = callbacks;

		const paymentParams = {
			provider: 'wxpay',
			timeStamp: String(paymentData.timestamp),
			nonceStr: paymentData.nonceStr,
			package: "prepay_id=" + paymentData.prepayId,
			signType: 'MD5',
			paySign: paymentData.sign,
			appId: paymentData.appId
		};

		console.log('小程序支付参数:', JSON.stringify(paymentParams));

		return new Promise((resolve, reject) => {
			uni.requestPayment({
				...paymentParams,
				success: (res) => {
					console.log('小程序支付成功', res);
					this.handlePaymentSuccess(onSuccess, subscribeConfig, successRedirectUrl);
					resolve(res);
				},
				fail: (err) => {
					console.error('小程序支付失败:', err);
					this.handlePaymentError(err, onFail, onCancel);
					reject(err);
				}
			});
		});
	}

	/**
	 * H5微信支付处理
	 * @param {Object} paymentData 支付数据
	 * @param {Object} callbacks 回调函数集合
	 */
	async handleH5WechatPay(paymentData, callbacks = {}) {
		const { onSuccess, onFail, onCancel, successRedirectUrl } = callbacks;

		if (typeof WeixinJSBridge === "undefined") {
			throw new Error('当前环境不支持微信支付');
		}

		return new Promise((resolve, reject) => {
			WeixinJSBridge.invoke('getBrandWCPayRequest', {
				appId: paymentData.appId,
				timeStamp: String(paymentData.timestamp),
				nonceStr: paymentData.nonceStr,
				package: "prepay_id=" + paymentData.prepayId,
				signType: 'MD5',
				paySign: paymentData.sign
			}, (res) => {
				if (res.err_msg === "get_brand_wcpay_request:ok") {
					console.log('H5微信支付成功', res);
					this.handlePaymentSuccess(onSuccess, null, successRedirectUrl);
					resolve(res);
				} else {
					console.error('H5微信支付失败:', res);
					this.handlePaymentError(res, onFail, onCancel);
					reject(res);
				}
			});
		});
	}

	/**
	 * 处理支付成功
	 * @param {Function} onSuccess 成功回调
	 * @param {Object} subscribeConfig 订阅配置
	 * @param {string} successRedirectUrl 成功跳转页面
	 */
	handlePaymentSuccess(onSuccess, subscribeConfig, successRedirectUrl) {
		uni.showToast({
			title: '支付成功',
			icon: 'success'
		});

		// 执行自定义成功回调
		if (onSuccess && typeof onSuccess === 'function') {
			onSuccess();
		}

		// 处理订阅消息（仅小程序）
		if (this.platform === 'mp-weixin' && subscribeConfig) {
			this.handleSubscribeMessage(subscribeConfig);
		}

		// 延迟跳转
		setTimeout(() => {
			if (successRedirectUrl) {
				uni.redirectTo({
					url: successRedirectUrl
				});
			}
		}, 1000);
	}

	/**
	 * 处理支付错误
	 * @param {Object} error 错误对象
	 * @param {Function} onFail 失败回调
	 * @param {Function} onCancel 取消回调
	 */
	handlePaymentError(error, onFail, onCancel) {
		const errorMsg = error.errMsg || error.err_msg || '';
		
		if (errorMsg.includes('cancel')) {
			// 用户取消支付
			uni.showToast({
				title: '您已取消支付',
				icon: 'none'
			});
			if (onCancel && typeof onCancel === 'function') {
				onCancel(error);
			}
		} else {
			// 支付失败
			uni.showToast({
				title: '支付失败，请稍后重试',
				icon: 'none'
			});
			if (onFail && typeof onFail === 'function') {
				onFail(error);
			}
		}
	}

	/**
	 * 处理订阅消息（仅微信小程序）
	 * @param {Object} config 订阅配置
	 */
	handleSubscribeMessage(config = {}) {
		const { tmplIds, onSubscribeSuccess, onSubscribeFail } = config;
		
		if (!tmplIds) {
			console.warn('订阅消息模板ID未配置');
			return;
		}

		uni.requestSubscribeMessage({
			provider: 'weixin',
			tmplIds: Array.isArray(tmplIds) ? tmplIds : [tmplIds],
			success: (res) => {
				console.log('订阅消息结果:', res);
				const anyAccepted = Object.values(res).some(status => status === 'accept');
				if (anyAccepted) {
					uni.showToast({
						title: '消息订阅成功',
						icon: 'success'
					});
				}
				if (onSubscribeSuccess && typeof onSubscribeSuccess === 'function') {
					onSubscribeSuccess(res);
				}
			},
			fail: (err) => {
				console.error('订阅消息失败:', err);
				if (onSubscribeFail && typeof onSubscribeFail === 'function') {
					onSubscribeFail(err);
				}
			}
		});
	}

	/**
	 * 快速支付方法（简化版）
	 * @param {Object} paymentData 支付数据
	 * @param {string} successUrl 成功跳转页面（可选）
	 * @returns {Promise}
	 */
	async quickPay(paymentData, successUrl) {
		return this.unifiedPay({
			paymentData,
			successRedirectUrl: successUrl
		});
	}
}

// 创建单例实例
const paymentUtils = new PaymentUtils();

export default paymentUtils;
