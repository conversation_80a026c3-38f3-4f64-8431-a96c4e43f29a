<template>
    <view class="page">
        <u-picker
            v-if="flag"
            :show="showCity"
            ref="uPicker"
            :loading="loading"
            :columns="columnsCity"
            @change="changeHandler"
            keyName="title"
            @cancel="showCity = false"
            @confirm="confirmCity"
        ></u-picker>
        <u-modal
            :show="showMoney"
            :showCancelButton="true"
            cancelText="再想想"
            @confirm="confirmMoney"
            @cancel="showMoney = false"
        >
            <view class="slot-content">
                <rich-text :nodes="contentMoney"></rich-text>
            </view>
        </u-modal>
        <u-modal
            :show="show"
            :title="title"
            :showCancelButton="true"
            confirmText="同意"
            cancelText="不同意"
            @confirm="confirmModel"
            @cancel="cancelModel"
        >
            <view class="slot-content">
                <rich-text :nodes="entryNotice"></rich-text>
            </view>
        </u-modal>
        <u-modal
            v-if="shInfo.status == 4"
            :show="showSh"
            title="驳回原因"
            confirmText="确定"
            @confirm="showSh = false"
            :content="shInfo.sh_text"
        ></u-modal>
        <view
            class="header"
            v-if="shInfo.status"
            :style="'color:' + arr[shInfo.status - 1].color"
            @click="shDetail"
        >
            {{ arr[shInfo.status - 1].text }}
        </view>
        <view class="main">
            <view class="main_item">
                <view class="title"><span>*</span>姓名</view>
                <input type="text" v-model="form.coachName" placeholder="请输入姓名" />
            </view>
            <view class="main_item">
                <view class="title"><span>*</span>手机号</view>
                <input type="text" v-model="form.mobile" placeholder="请输入手机号" />
            </view>
            <view class="main_item">
                <view class="title"><span>*</span>性别</view>
                <u-radio-group v-model="form.sex" placement="row">
                    <u-radio :customStyle="{ marginRight: '20px' }" label="男" :name="0"></u-radio>
                    <u-radio label="女" :name="1"></u-radio>
                </u-radio-group>
            </view>
            <view class="main_item">
                <view class="title"><span>*</span>从业年份</view>
                <input type="text" v-model="form.workTime" placeholder="请输入从业年份" />
            </view>
            <view class="main_item">
                <view class="title"><span>*</span>选择区域</view>
                <input
                    type="text"
                    v-model="form.city"
                    placeholder="请选择代理区域"
                    disabled
                    @click="showCity = true"
                />
            </view>
            <view class="main_item">
                <view class="title"><span>*</span>详细地址</view>
                <input type="text" v-model="form.address" placeholder="请输入详细地址" />
            </view>
            <view class="main_item">
                <view class="title"><span>*</span>自我介绍(非必填)</view>
                <input type="text" v-model="form.text" placeholder="请输入自我介绍" />
            </view>
            <view class="main_item">
                <view class="title"><span>*</span>身份证号</view>
                <input type="text" v-model="form.idCode" placeholder="请输入身份证号" />
            </view>
            <view class="main_item">
                <view class="title"><span>*</span>上传身份证照片</view>
                <view class="card">
                    <view class="card_item">
                        <view class="top">
                            <view class="das">
                                <view class="up">
                                    <upload
                                        @upload="imgUpload"
                                        @del="imgUpload"
                                        :imagelist="form.id_card1"
                                        imgtype="id_card1"
                                        imgclass="id_card_box"
                                        text="身份证人像面"
                                        :imgsize="1"
                                    ></upload>
                                </view>
                            </view>
                        </view>
                        <view class="bottom">拍摄人像面</view>
                    </view>
                    <view class="card_item">
                        <view class="top">
                            <view class="das">
                                <view class="up">
                                    <upload
                                        @upload="imgUpload"
                                        @del="imgUpload"
                                        :imagelist="form.id_card2"
                                        imgtype="id_card2"
                                        imgclass="id_card_box"
                                        text="身份证国徽面"
                                        :imgsize="1"
                                    ></upload>
                                </view>
                            </view>
                        </view>
                        <view class="bottom">拍摄国徽面</view>
                    </view>
                </view>
            </view>
            <view class="main_item">
                <view class="title"><span>*</span>上传形象照片</view>
                <upload
                    @upload="imgUpload"
                    @del="imgUpload"
                    :imagelist="form.self_img"
                    imgtype="self_img"
                    imgclass=""
                    text="形象照片"
                    :imgsize="3"
                ></upload>
            </view>
        </view>
        <view class="footer" v-if="!shInfo.status">
            <view class="btn" @click="submit">立即提交</view>
        </view>
    </view>
</template>

<script>
import Upload from '@/components/upload.vue'; // Adjust path to your upload.vue

export default {
    components: {
        Upload,
    },
    data() {
        return {
            flag: false,
            showSh: false,
            shInfo: {},
            title: '入驻须知',
            contentMoney: '',
            show: false,
            arr: [
                { text: '信息审核中，请稍作等待', color: '#FE921B' },
                { text: '审核成功', color: '#07C160' },
                {},
                { text: '审核失败>', color: '#E72427' },
            ],
            form: {
                userId: '',
                coachName: '',
                sex: 0,
                mobile: '',
                workTime: '',
                city: '',
                cityId: [],
                lng: '',
                lat: '',
                address: '',
                idCode: '',
                text: '',
                id_card1: [], // Changed from idCard to id_card1
                id_card2: [],
                self_img: [],
            },
            showMoney: false,
            entryNotice: '',
            showCity: false,
            loading: false,
            columnsCity: [[], [], []],
        };
    },
    methods: {
        // 检查当前平台
        getCurrentPlatform() {
            // #ifdef APP-PLUS
            return 'app-plus';
            // #endif
            // #ifdef MP-WEIXIN
            return 'mp-weixin';
            // #endif
            // #ifdef H5
            return 'h5';
            // #endif
            return 'unknown';
        },
        confirmCity(Array) {
            this.form.city = Array.value
                .map((item, index) => {
                    if (item == undefined) {
                        return this.columnsCity[index][0].title;
                    } else {
                        return item.title;
                    }
                })
                .join('-');
            this.form.cityId = Array.value.map((e, j) => {
                if (e == undefined) {
                    return this.columnsCity[j][0].id;
                } else {
                    return e.id;
                }
            });
            this.showCity = false;
        },
        changeHandler(e) {
            const {
                columnIndex,
                index,
                picker = this.$refs.uPicker,
            } = e;
            if (columnIndex === 0) {
                this.$api.service.getCity(this.columnsCity[0][index].id).then((res) => {
                    picker.setColumnValues(1, res);
                    this.columnsCity[1] = res;
                    this.$api.service.getCity(res[0].id).then((res1) => {
                        picker.setColumnValues(2, res1);
                        this.columnsCity[2] = res1;
                    });
                });
            } else if (columnIndex === 1) {
                this.$api.service.getCity(this.columnsCity[1][index].id).then((res) => {
                    picker.setColumnValues(2, res);
                    this.columnsCity[2] = res;
                });
            }
        },
        shDetail() {
            if (this.shInfo.status != 4) return;
            this.showSh = true;
        },
        imgUpload(e) {
            console.log('imgUpload event:', e);
            const { imagelist, imgtype } = e;
            this.$set(this.form, imgtype, imagelist); // Use $set for reactivity
        },
        getcon() {
            this.$api.service.getConfig().then((res) => {
                this.contentMoney = `入驻需交<span style="color:red;">${res.data.cashPledge}元</span>押金，是否申请`;
                this.entryNotice = res.entryNotice;
            });
        },
        // async confirmMoney() {
        //     this.showMoney = false;
        //     try {
        //         const res = await this.$api.service.masterPayY();
        //         const obj = res.pay_list;
        //         uni.requestPayment({
        //             provider: 'wxpay',
        //             timeStamp: obj.timeStamp,
        //             nonceStr: obj.nonceStr,
        //             package: obj.package,
        //             signType: obj.signType,
        //             paySign: obj.paySign,
        //             success: async () => {
        //                 uni.showToast({
        //                     title: '支付成功',
        //                     icon: 'success',
        //                     duration: 1500,
        //                 });
        //                 let subForm = JSON.parse(JSON.stringify(this.form));
        //                 subForm.id_card = subForm.id_card1.concat(subForm.id_card2);
        //                 delete subForm.id_card1;
        //                 delete subForm.id_card2;
        //                 delete subForm.city;

        //                 subForm.id_card = subForm.id_card.map((item) => item.path);
        //                 subForm.self_img = subForm.self_img.map((item) => item.path);

        //                 const res2 = await this.$api.service.masterIn(subForm);
        //                 if (res2) {
        //                     uni.navigateTo({
        //                         url: '/pages/apply_over',
        //                     });
        //                 }
        //             },
        //             fail: (err) => {
        //                 uni.showToast({
        //                     title: '支付失败请检查网络',
        //                     icon: 'error',
        //                 });
        //             },
        //         });
        //     } catch (error) {
        //         uni.showToast({
        //             title: error.message || '支付失败，请重试',
        //             icon: 'error',
        //         });
        //     }
        // },
		async confirmMoney() {
		    this.showMoney = false;
		    try {
		        const res = await this.$api.service.masterPayY();
		        const obj = res.pay_list;
		        uni.requestPayment({
		            provider: 'wxpay',
		            timeStamp: obj.timeStamp,
		            nonceStr: obj.nonceStr,
		            package: obj.package,
		            signType: obj.signType,
		            paySign: obj.paySign,
		            success: async () => {
		                uni.showToast({
		                    title: '支付成功',
		                    icon: 'success',
		                    duration: 1500,
		                });
		                let subForm = JSON.parse(JSON.stringify(this.form));
		                subForm.id_card = subForm.id_card1.concat(subForm.id_card2);
		                delete subForm.id_card1;
		                delete subForm.id_card2;
		                delete subForm.city;
		
		                subForm.id_card = subForm.id_card.map((item) => item.path);
		                subForm.self_img = subForm.self_img.map((item) => item.path);
		
		                const res2 = await this.$api.service.masterEnter(subForm);
						console.log(res2)
		                if (res2) {
		                    uni.navigateTo({
		                        url: '/pages/apply_over',
		                    });
		                }
		            },
		            fail: (err) => {
		                uni.showToast({
		                    title: '支付失败请检查网络',
		                    icon: 'error',
		                });
		            },
		        });
		    } catch (error) {
		        uni.showToast({
		            title: error.message || '支付失败，请重试',
		            icon: 'error',
		        });
		    }
		},
        submit() {
            for (let key in this.form) {
                if (this.form[key] === '' || (Array.isArray(this.form[key]) && this.form[key].length === 0)) {
                    uni.showToast({
                        icon: 'none',
                        title: '请填写完整提交',
                    });
                    return;
                }
            }
            if (this.form.id_card1.length === 0 || this.form.id_card2.length === 0 || this.form.self_img.length === 0) {
                uni.showToast({
                    icon: 'none',
                    title: '请上传所需图片',
                });
                return;
            }
            let p = /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/;
            if (!p.test(this.form.idCode)) {
                uni.showToast({
                    icon: 'none',
                    title: '请填写正确的身份证号',
                });
                return;
            }
            let phoneReg = /^1[3456789]\d{9}$/;
            if (!phoneReg.test(this.form.mobile)) {
                uni.showToast({
                    icon: 'none',
                    title: '请填写正确的手机号',
                    duration: 1000,
                });
                return;
            }
            this.showMoney = true;
        },
        cancelModel() {
            uni.navigateBack();
        },
        confirmModel() {
            this.show = false;
        },
        getcity(e) {
            this.$api.service.getCity(e).then((res) => {
                this.columnsCity[0] = res;
                this.$api.service.getCity(res[0].id).then((res1) => {
                    this.columnsCity[1] = res1;
                    this.$api.service.getCity(res1[0].id).then((res2) => {
                        this.columnsCity[2] = res2;
                        this.flag = true;
                    });
                });
            });
        },
        seeInfo() {
            this.$api.service.masterSee().then((res) => {
                if (res && Object.keys(res).length !== 0) {
                    this.shInfo = res;
                    this.form = {
                        ...this.form,
                        ...res,
                        id_card1: res.id_card && res.id_card[0] ? [{ path: res.id_card[0] }] : [],
                        id_card2: res.id_card && res.id_card[1] ? [{ path: res.id_card[1] }] : [],
                        self_img: res.self_img ? res.self_img.map((item) => ({ path: item })) : [],
                    };
                } else {
                    this.show = true;
                }
            });
        },
    },
    onLoad() {
        this.getcon();
        this.form.userId = this.$store.state.user.userInfo.id;
        const { city_id, lng, lat } = this.$store.state.user.userInfo;
        this.form.cityId = city_id;
        this.form.lng = lng;
        this.form.lat = lat;
        this.getcity(0);
        this.seeInfo();
    },
    onShow() {
        uni.$on('getShInfo', (data) => {
            if (data) {
                this.seeInfo();
            }
        });
    },
    onUnload() {
        uni.$off('getShInfo');
    },
};
</script>

<style scoped lang="scss">
.page {
    padding-bottom: 200rpx;

    .header {
        width: 750rpx;
        height: 58rpx;
        background: #fff7f1;
        line-height: 58rpx;
        text-align: center;
        font-size: 28rpx;
        font-weight: 400;
    }

    .main {
        padding: 40rpx 30rpx;

        .main_item {
            margin-bottom: 20rpx;

            .title {
                margin-bottom: 20rpx;
                font-size: 28rpx;
                font-weight: 400;
                color: #333333;

                span {
                    color: #e72427;
                }
            }

            input {
                width: 690rpx;
                height: 110rpx;
                background: #f8f8f8;
                font-size: 28rpx;
                font-weight: 400;
                line-height: 110rpx;
                padding: 0 40rpx;
                box-sizing: border-box;
            }

            .card {
                display: flex;
                align-items: center;
                justify-content: space-between;

                .card_item {
                    width: 332rpx;
                    height: 332rpx;
                    background: #f2fafe;
                    border-radius: 16rpx;
                    overflow: hidden;

                    .top {
                        height: 266rpx;
                        width: 100%;
                        padding-top: 40rpx;

                        .das {
                            margin: 0 auto;
                            width: 266rpx;
                            height: 180rpx;
                            border: 2rpx dashed #2e80fe;
                            padding-top: 28rpx;

                            .up {
                                margin: 0 auto;
                                width: 210rpx;
                                height: 130rpx;
                            }
                        }
                    }

                    .bottom {
                        height: 66rpx;
                        width: 100%;
                        background-color: #2e80fe;
                        font-size: 28rpx;
                        font-weight: 400;
                        color: #ffffff;
                        text-align: center;
                        line-height: 66rpx;
                    }
                }
            }
        }
    }

    .footer {
        padding: 52rpx 30rpx;
        width: 750rpx;
        background: #ffffff;
        box-shadow: 0rpx 0rpx 6rpx 2rpx rgba(193, 193, 193, 0.3);
        position: fixed;
        bottom: 0;

        .btn {
            width: 690rpx;
            height: 98rpx;
            background: #2e80fe;
            border-radius: 50rpx;
            font-size: 32rpx;
            font-weight: 500;
            color: #ffffff;
            line-height: 98rpx;
            text-align: center;
        }
    }
}
</style>